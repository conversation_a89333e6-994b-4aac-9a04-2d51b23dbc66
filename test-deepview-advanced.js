#!/usr/bin/env node

/**
 * Advanced DeepView Path Testing
 * Tests various edge cases and scenarios for path handling
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AdvancedDeepViewTester {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.testResults = [];
  }

  /**
   * Test different path formats and edge cases
   */
  async testPathEdgeCases() {
    console.log('\n🧪 Testing Path Edge Cases:');
    console.log('='.repeat(50));

    const testCases = [
      // Basic relative paths
      { path: 'package.json', type: 'basic-relative' },
      { path: './package.json', type: 'dot-relative' },
      { path: 'src/index.ts', type: 'nested-relative' },
      { path: './src/index.ts', type: 'dot-nested-relative' },
      
      // Absolute paths
      { path: path.resolve(this.workspaceRoot, 'package.json'), type: 'absolute' },
      { path: path.resolve(this.workspaceRoot, 'src/index.ts'), type: 'absolute-nested' },
      
      // Path with backslashes (Windows style)
      { path: 'src\\index.ts', type: 'windows-style' },
      { path: 'src\\services\\generic\\tools.ts', type: 'windows-nested' },
      
      // Non-existent paths
      { path: 'missing-file.txt', type: 'missing-relative' },
      { path: path.resolve(this.workspaceRoot, 'missing-file.txt'), type: 'missing-absolute' },
      
      // Parent directory references
      { path: '../google-cloud-mcp/package.json', type: 'parent-reference' },
      
      // Current directory references
      { path: '.', type: 'current-dir' },
      { path: './src', type: 'current-dir-nested' },
    ];

    for (const testCase of testCases) {
      await this.testSinglePath(testCase);
    }
  }

  /**
   * Test a single path case
   */
  async testSinglePath({ path: testPath, type }) {
    console.log(`\n📁 Testing ${type}: ${testPath}`);
    
    try {
      // Normalize the path
      const normalizedPath = path.normalize(testPath);
      console.log(`  Normalized: ${normalizedPath}`);
      
      // Resolve to absolute path
      const absolutePath = path.resolve(this.workspaceRoot, testPath);
      console.log(`  Resolved: ${absolutePath}`);
      
      // Check if path exists
      const exists = fs.existsSync(testPath);
      const absoluteExists = fs.existsSync(absolutePath);
      
      console.log(`  Original path exists: ${exists ? '✅' : '❌'}`);
      console.log(`  Absolute path exists: ${absoluteExists ? '✅' : '❌'}`);
      
      if (exists || absoluteExists) {
        const pathToUse = exists ? testPath : absolutePath;
        const stats = fs.statSync(pathToUse);
        
        if (stats.isDirectory()) {
          console.log(`  📂 Directory (${this.getDirectorySize(pathToUse)} items)`);
        } else if (stats.isFile()) {
          console.log(`  📄 File (${stats.size} bytes)`);
          
          // Try to read file content safely
          try {
            const content = fs.readFileSync(pathToUse, 'utf8');
            const lineCount = content.split('\n').length;
            console.log(`  📊 Lines: ${lineCount}`);
          } catch (readError) {
            console.log(`  ⚠️  Could not read file content: ${readError.message}`);
          }
        }
      }
      
      // Record test result
      this.testResults.push({
        type,
        path: testPath,
        normalizedPath,
        absolutePath,
        exists,
        absoluteExists,
        success: exists || absoluteExists
      });
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      this.testResults.push({
        type,
        path: testPath,
        error: error.message,
        success: false
      });
    }
  }

  /**
   * Get directory size (number of items)
   */
  getDirectorySize(dirPath) {
    try {
      return fs.readdirSync(dirPath).length;
    } catch {
      return 0;
    }
  }

  /**
   * Test path resolution consistency
   */
  async testPathResolutionConsistency() {
    console.log('\n🔄 Testing Path Resolution Consistency:');
    console.log('='.repeat(50));

    const testPaths = [
      'package.json',
      'src/index.ts',
      'src/services/generic/tools.ts'
    ];

    for (const testPath of testPaths) {
      console.log(`\n🔍 Testing: ${testPath}`);
      
      // Different ways to reference the same file
      const variations = [
        testPath,
        `./${testPath}`,
        path.resolve(this.workspaceRoot, testPath),
        path.join(this.workspaceRoot, testPath)
      ];
      
      const results = variations.map(variation => {
        try {
          const exists = fs.existsSync(variation);
          const resolved = path.resolve(variation);
          return { variation, exists, resolved };
        } catch (error) {
          return { variation, exists: false, error: error.message };
        }
      });
      
      // Check if all variations resolve to the same absolute path
      const resolvedPaths = results
        .filter(r => r.resolved)
        .map(r => r.resolved);
      
      const allSame = resolvedPaths.every(p => p === resolvedPaths[0]);
      
      console.log(`  All variations resolve to same path: ${allSame ? '✅' : '❌'}`);
      
      results.forEach((result, i) => {
        const status = result.exists ? '✅' : '❌';
        console.log(`    ${i + 1}. ${result.variation} ${status}`);
        if (result.resolved) {
          console.log(`       → ${result.resolved}`);
        }
      });
    }
  }

  /**
   * Generate summary report
   */
  generateSummaryReport() {
    console.log('\n📊 Summary Report:');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - successfulTests;
    
    console.log(`Total tests: ${totalTests}`);
    console.log(`Successful: ${successfulTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success rate: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
    
    // Group by test type
    const byType = this.testResults.reduce((acc, result) => {
      if (!acc[result.type]) acc[result.type] = [];
      acc[result.type].push(result);
      return acc;
    }, {});
    
    console.log('\n📋 Results by type:');
    Object.entries(byType).forEach(([type, results]) => {
      const successful = results.filter(r => r.success).length;
      const total = results.length;
      console.log(`  ${type}: ${successful}/${total} ✅`);
    });
  }
}

/**
 * Main test runner
 */
async function runAdvancedDeepViewTests() {
  console.log('🚀 Advanced DeepView Path Testing');
  console.log(`Working directory: ${process.cwd()}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Node.js: ${process.version}`);
  
  const tester = new AdvancedDeepViewTester();
  
  try {
    await tester.testPathEdgeCases();
    await tester.testPathResolutionConsistency();
    tester.generateSummaryReport();
    
    console.log('\n✅ Advanced DeepView testing completed successfully!');
  } catch (error) {
    console.error('\n❌ Testing failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
runAdvancedDeepViewTests().catch(console.error);
