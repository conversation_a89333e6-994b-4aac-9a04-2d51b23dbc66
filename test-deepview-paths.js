#!/usr/bin/env node

/**
 * Test script to demonstrate path handling with both relative and absolute paths
 * This simulates what deepview might do for file analysis
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Simulated deepview functionality for testing paths
 */
class DeepViewSimulator {
  constructor() {
    this.workspaceRoot = process.cwd();
  }

  /**
   * Analyze files with relative paths
   */
  async analyzeWithRelativePaths(filePaths) {
    console.log('\n🔍 Testing DeepView with RELATIVE paths:');
    console.log('='.repeat(50));
    
    for (const filePath of filePaths) {
      try {
        console.log(`\nAnalyzing: ${filePath}`);
        
        // Resolve relative path
        const fullPath = path.resolve(this.workspaceRoot, filePath);
        console.log(`  Resolved to: ${fullPath}`);
        
        // Check if file exists
        if (fs.existsSync(fullPath)) {
          const stats = fs.statSync(fullPath);
          console.log(`  ✅ File exists (${stats.size} bytes)`);
          
          // Read first few lines for analysis
          if (stats.isFile()) {
            const content = fs.readFileSync(fullPath, 'utf8');
            const lines = content.split('\n').slice(0, 3);
            console.log(`  📄 First 3 lines:`);
            lines.forEach((line, i) => {
              console.log(`    ${i + 1}: ${line.substring(0, 80)}${line.length > 80 ? '...' : ''}`);
            });
          }
        } else {
          console.log(`  ❌ File not found`);
        }
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`);
      }
    }
  }

  /**
   * Analyze files with absolute paths
   */
  async analyzeWithAbsolutePaths(filePaths) {
    console.log('\n🔍 Testing DeepView with ABSOLUTE paths:');
    console.log('='.repeat(50));
    
    for (const filePath of filePaths) {
      try {
        console.log(`\nAnalyzing: ${filePath}`);
        
        // Use absolute path directly
        console.log(`  Using absolute path: ${filePath}`);
        
        // Check if file exists
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          console.log(`  ✅ File exists (${stats.size} bytes)`);
          
          // Read first few lines for analysis
          if (stats.isFile()) {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').slice(0, 3);
            console.log(`  📄 First 3 lines:`);
            lines.forEach((line, i) => {
              console.log(`    ${i + 1}: ${line.substring(0, 80)}${line.length > 80 ? '...' : ''}`);
            });
          }
        } else {
          console.log(`  ❌ File not found`);
        }
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`);
      }
    }
  }

  /**
   * Compare relative vs absolute path handling
   */
  async comparePathHandling(testFiles) {
    console.log('\n📊 Comparing Relative vs Absolute Path Handling:');
    console.log('='.repeat(60));
    
    for (const file of testFiles) {
      console.log(`\nTesting file: ${file}`);
      
      // Test relative path
      const relativePath = file;
      const absolutePath = path.resolve(this.workspaceRoot, file);
      
      console.log(`  Relative: ${relativePath}`);
      console.log(`  Absolute: ${absolutePath}`);
      
      // Check both
      const relativeExists = fs.existsSync(relativePath);
      const absoluteExists = fs.existsSync(absolutePath);
      
      console.log(`  Relative exists: ${relativeExists ? '✅' : '❌'}`);
      console.log(`  Absolute exists: ${absoluteExists ? '✅' : '❌'}`);
      
      if (relativeExists && absoluteExists) {
        console.log(`  🎯 Both paths work correctly`);
      } else if (absoluteExists) {
        console.log(`  ⚠️  Only absolute path works`);
      } else if (relativeExists) {
        console.log(`  ⚠️  Only relative path works`);
      } else {
        console.log(`  ❌ Neither path works`);
      }
    }
  }
}

/**
 * Main test function
 */
async function runDeepViewPathTests() {
  console.log('🚀 DeepView Path Testing Simulator');
  console.log('Current working directory:', process.cwd());
  
  const deepview = new DeepViewSimulator();
  
  // Test files - mix of existing and non-existing files
  const testFiles = [
    'package.json',
    'README.md',
    'src/index.ts',
    'src/services/generic/tools.ts',
    'nonexistent-file.txt',
    'src/nonexistent-service/test.ts'
  ];
  
  // Convert to absolute paths for absolute path testing
  const absolutePaths = testFiles.map(file => 
    path.resolve(process.cwd(), file)
  );
  
  // Run tests
  await deepview.analyzeWithRelativePaths(testFiles);
  await deepview.analyzeWithAbsolutePaths(absolutePaths);
  await deepview.comparePathHandling(testFiles);
  
  console.log('\n✅ DeepView path testing completed!');
}

// Run the tests
runDeepViewPathTests().catch(console.error);
