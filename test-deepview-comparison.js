#!/usr/bin/env node

/**
 * DeepView Path Comparison Test
 * Demonstrates the key differences between relative and absolute path handling
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DeepViewPathComparison {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.testScenarios = [];
  }

  /**
   * Test scenario: Working directory changes
   */
  async testWorkingDirectoryChanges() {
    console.log('\n🔄 Testing Working Directory Changes:');
    console.log('='.repeat(50));

    const originalCwd = process.cwd();
    const testFile = 'package.json';
    
    console.log(`Original working directory: ${originalCwd}`);
    
    // Test 1: From root directory
    console.log('\n📍 Test from root directory:');
    const relativeFromRoot = fs.existsSync(testFile);
    const absoluteFromRoot = fs.existsSync(path.resolve(originalCwd, testFile));
    console.log(`  Relative path (${testFile}): ${relativeFromRoot ? '✅' : '❌'}`);
    console.log(`  Absolute path: ${absoluteFromRoot ? '✅' : '❌'}`);
    
    // Test 2: From subdirectory
    try {
      const srcDir = path.join(originalCwd, 'src');
      if (fs.existsSync(srcDir)) {
        process.chdir(srcDir);
        console.log(`\n📍 Test from subdirectory (${process.cwd()}):`);
        
        const relativeFromSrc = fs.existsSync(testFile);
        const absoluteFromSrc = fs.existsSync(path.resolve(originalCwd, testFile));
        const relativeUpFromSrc = fs.existsSync('../package.json');
        
        console.log(`  Relative path (${testFile}): ${relativeFromSrc ? '✅' : '❌'}`);
        console.log(`  Relative up path (../package.json): ${relativeUpFromSrc ? '✅' : '❌'}`);
        console.log(`  Absolute path: ${absoluteFromSrc ? '✅' : '❌'}`);
        
        // Key insight
        console.log('\n💡 Key Insight:');
        console.log('  - Relative paths depend on current working directory');
        console.log('  - Absolute paths work regardless of working directory');
        console.log('  - This is why absolute paths are more reliable for tools like deepview');
      }
    } finally {
      // Always restore original working directory
      process.chdir(originalCwd);
    }
  }

  /**
   * Test scenario: Path normalization differences
   */
  async testPathNormalization() {
    console.log('\n🔧 Testing Path Normalization:');
    console.log('='.repeat(50));

    const testCases = [
      { input: 'src/index.ts', type: 'simple' },
      { input: './src/index.ts', type: 'dot-prefix' },
      { input: 'src/../src/index.ts', type: 'redundant-navigation' },
      { input: 'src/./index.ts', type: 'current-dir-reference' },
      { input: 'src\\index.ts', type: 'windows-separators' },
      { input: 'src//index.ts', type: 'double-separators' }
    ];

    for (const testCase of testCases) {
      console.log(`\n📝 Testing ${testCase.type}: "${testCase.input}"`);
      
      // Relative path handling
      const normalizedRelative = path.normalize(testCase.input);
      const relativeExists = fs.existsSync(testCase.input);
      
      // Absolute path handling
      const absolutePath = path.resolve(this.workspaceRoot, testCase.input);
      const normalizedAbsolute = path.normalize(absolutePath);
      const absoluteExists = fs.existsSync(absolutePath);
      
      console.log(`  Input: ${testCase.input}`);
      console.log(`  Normalized relative: ${normalizedRelative}`);
      console.log(`  Normalized absolute: ${normalizedAbsolute}`);
      console.log(`  Relative exists: ${relativeExists ? '✅' : '❌'}`);
      console.log(`  Absolute exists: ${absoluteExists ? '✅' : '❌'}`);
      
      // Check if they resolve to the same file
      if (relativeExists && absoluteExists) {
        const relativeResolved = path.resolve(testCase.input);
        const sameFile = relativeResolved === normalizedAbsolute;
        console.log(`  Same file: ${sameFile ? '✅' : '❌'}`);
      }
    }
  }

  /**
   * Test scenario: Cross-platform compatibility
   */
  async testCrossPlatformCompatibility() {
    console.log('\n🌐 Testing Cross-Platform Compatibility:');
    console.log('='.repeat(50));

    const testPaths = [
      'src/index.ts',           // Unix-style
      'src\\index.ts',          // Windows-style
      'src/services/generic/tools.ts',  // Deep Unix-style
      'src\\services\\generic\\tools.ts' // Deep Windows-style
    ];

    console.log(`Current platform: ${process.platform}`);
    console.log(`Path separator: "${path.sep}"`);

    for (const testPath of testPaths) {
      console.log(`\n🔍 Testing: "${testPath}"`);
      
      // Test relative path
      const relativeExists = fs.existsSync(testPath);
      const relativePosix = testPath.replace(/\\/g, '/');
      const relativeWin32 = testPath.replace(/\//g, '\\');
      
      // Test absolute path
      const absolutePath = path.resolve(this.workspaceRoot, testPath);
      const absoluteExists = fs.existsSync(absolutePath);
      
      console.log(`  Original: ${relativeExists ? '✅' : '❌'}`);
      console.log(`  POSIX style: ${fs.existsSync(relativePosix) ? '✅' : '❌'}`);
      console.log(`  Win32 style: ${fs.existsSync(relativeWin32) ? '✅' : '❌'}`);
      console.log(`  Absolute: ${absoluteExists ? '✅' : '❌'}`);
      console.log(`  Resolved to: ${absolutePath}`);
    }
  }

  /**
   * Test scenario: Performance comparison
   */
  async testPerformanceComparison() {
    console.log('\n⚡ Testing Performance Comparison:');
    console.log('='.repeat(50));

    const testFiles = [
      'package.json',
      'README.md',
      'src/index.ts',
      'src/services/generic/tools.ts'
    ];

    const iterations = 1000;

    // Test relative path performance
    console.log(`\n🏃 Testing relative paths (${iterations} iterations):`);
    const relativeStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      for (const file of testFiles) {
        fs.existsSync(file);
      }
    }
    const relativeEnd = performance.now();
    const relativeTime = relativeEnd - relativeStart;

    // Test absolute path performance
    console.log(`🏃 Testing absolute paths (${iterations} iterations):`);
    const absoluteStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      for (const file of testFiles) {
        const absolutePath = path.resolve(this.workspaceRoot, file);
        fs.existsSync(absolutePath);
      }
    }
    const absoluteEnd = performance.now();
    const absoluteTime = absoluteEnd - absoluteStart;

    console.log(`\n📊 Performance Results:`);
    console.log(`  Relative paths: ${relativeTime.toFixed(2)}ms`);
    console.log(`  Absolute paths: ${absoluteTime.toFixed(2)}ms`);
    console.log(`  Difference: ${Math.abs(relativeTime - absoluteTime).toFixed(2)}ms`);
    console.log(`  Faster: ${relativeTime < absoluteTime ? 'Relative' : 'Absolute'} paths`);
  }

  /**
   * Generate recommendations
   */
  generateRecommendations() {
    console.log('\n📋 DeepView Path Handling Recommendations:');
    console.log('='.repeat(60));

    console.log('\n✅ When to use RELATIVE paths:');
    console.log('  • Working within a stable working directory');
    console.log('  • Simple file operations in the same directory');
    console.log('  • Configuration files that reference nearby files');
    console.log('  • Slightly better performance for simple operations');

    console.log('\n✅ When to use ABSOLUTE paths:');
    console.log('  • Tools that may change working directory');
    console.log('  • Cross-platform compatibility requirements');
    console.log('  • Complex file analysis tools (like deepview)');
    console.log('  • When path resolution must be guaranteed');
    console.log('  • Multi-threaded or async operations');

    console.log('\n🎯 Best Practices for DeepView-like tools:');
    console.log('  1. Always resolve relative paths to absolute paths internally');
    console.log('  2. Normalize paths using path.normalize() and path.resolve()');
    console.log('  3. Use path.sep for cross-platform separator handling');
    console.log('  4. Validate file existence before processing');
    console.log('  5. Handle both input formats but standardize internally');

    console.log('\n⚠️  Common Pitfalls:');
    console.log('  • Assuming working directory remains constant');
    console.log('  • Not handling different path separators');
    console.log('  • Forgetting to normalize paths with .. or . references');
    console.log('  • Not validating file existence before processing');
  }
}

/**
 * Main test runner
 */
async function runDeepViewComparison() {
  console.log('🚀 DeepView Path Handling Comparison');
  console.log(`Workspace: ${process.cwd()}`);
  console.log(`Platform: ${process.platform}`);
  console.log(`Node.js: ${process.version}`);

  const tester = new DeepViewPathComparison();

  try {
    await tester.testWorkingDirectoryChanges();
    await tester.testPathNormalization();
    await tester.testCrossPlatformCompatibility();
    await tester.testPerformanceComparison();
    tester.generateRecommendations();

    console.log('\n🎉 DeepView path comparison testing completed!');
  } catch (error) {
    console.error('\n❌ Testing failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the comparison tests
runDeepViewComparison().catch(console.error);
